<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Demo - Test các chức năng</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .api-test {
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .response {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 3px;
            padding: 10px;
            margin-top: 10px;
            white-space: pre-wrap;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1>API Demo - Test các chức năng</h1>
        <p class="text-muted">Trang này giúp bạn test các API endpoint một cách trực quan</p>

        <!-- GET Products -->
        <div class="api-test">
            <h3>1. GET - L<PERSON>y danh sách sản phẩm</h3>
            <button class="btn btn-primary" onclick="testGetProducts()">Test GET /api/product</button>
            <div id="get-products-response" class="response" style="display: none;"></div>
        </div>

        <!-- GET Categories -->
        <div class="api-test">
            <h3>2. GET - Lấy danh sách danh mục</h3>
            <button class="btn btn-primary" onclick="testGetCategories()">Test GET /api/category</button>
            <div id="get-categories-response" class="response" style="display: none;"></div>
        </div>

        <!-- POST Product -->
        <div class="api-test">
            <h3>3. POST - Thêm sản phẩm mới</h3>
            <div class="row">
                <div class="col-md-6">
                    <input type="text" class="form-control mb-2" id="post-name" placeholder="Tên sản phẩm" value="Sản phẩm test">
                    <textarea class="form-control mb-2" id="post-description" placeholder="Mô tả">Mô tả sản phẩm test</textarea>
                </div>
                <div class="col-md-6">
                    <input type="number" class="form-control mb-2" id="post-price" placeholder="Giá" value="100.00" step="0.01">
                    <select class="form-control mb-2" id="post-category">
                        <option value="">-- Chọn danh mục --</option>
                    </select>
                </div>
            </div>
            <button class="btn btn-success" onclick="testPostProduct()">Test POST /api/product</button>
            <div id="post-product-response" class="response" style="display: none;"></div>
        </div>

        <!-- GET Single Product -->
        <div class="api-test">
            <h3>4. GET - Lấy thông tin một sản phẩm</h3>
            <div class="input-group mb-2">
                <input type="number" class="form-control" id="get-product-id" placeholder="ID sản phẩm" value="1">
                <button class="btn btn-primary" onclick="testGetProduct()">Test GET /api/product/{id}</button>
            </div>
            <div id="get-product-response" class="response" style="display: none;"></div>
        </div>

        <!-- PUT Product -->
        <div class="api-test">
            <h3>5. PUT - Cập nhật sản phẩm</h3>
            <div class="row">
                <div class="col-md-2">
                    <input type="number" class="form-control mb-2" id="put-id" placeholder="ID" value="1">
                </div>
                <div class="col-md-5">
                    <input type="text" class="form-control mb-2" id="put-name" placeholder="Tên sản phẩm" value="Sản phẩm đã cập nhật">
                    <textarea class="form-control mb-2" id="put-description" placeholder="Mô tả">Mô tả đã được cập nhật</textarea>
                </div>
                <div class="col-md-5">
                    <input type="number" class="form-control mb-2" id="put-price" placeholder="Giá" value="200.00" step="0.01">
                    <select class="form-control mb-2" id="put-category">
                        <option value="">-- Chọn danh mục --</option>
                    </select>
                </div>
            </div>
            <button class="btn btn-warning" onclick="testPutProduct()">Test PUT /api/product/{id}</button>
            <div id="put-product-response" class="response" style="display: none;"></div>
        </div>

        <!-- DELETE Product -->
        <div class="api-test">
            <h3>6. DELETE - Xóa sản phẩm</h3>
            <div class="input-group mb-2">
                <input type="number" class="form-control" id="delete-product-id" placeholder="ID sản phẩm">
                <button class="btn btn-danger" onclick="testDeleteProduct()">Test DELETE /api/product/{id}</button>
            </div>
            <div class="alert alert-warning">
                <strong>Cảnh báo:</strong> Thao tác này sẽ xóa vĩnh viễn sản phẩm!
            </div>
            <div id="delete-product-response" class="response" style="display: none;"></div>
        </div>

        <!-- API Status -->
        <div class="api-test">
            <h3>7. Trạng thái API</h3>
            <button class="btn btn-info" onclick="checkApiStatus()">Kiểm tra trạng thái API</button>
            <div id="api-status-response" class="response" style="display: none;"></div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        const baseUrl = '/project1';

        // Load categories on page load
        $(document).ready(function() {
            loadCategoriesForDemo();
        });

        function loadCategoriesForDemo() {
            $.ajax({
                url: `${baseUrl}/api/category`,
                method: 'GET',
                success: function(data) {
                    const selects = ['#post-category', '#put-category'];
                    selects.forEach(function(selector) {
                        const select = $(selector);
                        select.find('option:not(:first)').remove();
                        data.forEach(function(category) {
                            select.append(`<option value="${category.id}">${category.name}</option>`);
                        });
                    });
                },
                error: function(xhr, status, error) {
                    console.error('Error loading categories:', error);
                }
            });
        }

        function testGetProducts() {
            makeApiCall('GET', `${baseUrl}/api/product`, null, 'get-products-response');
        }

        function testGetCategories() {
            makeApiCall('GET', `${baseUrl}/api/category`, null, 'get-categories-response');
        }

        function testPostProduct() {
            const data = {
                name: $('#post-name').val(),
                description: $('#post-description').val(),
                price: $('#post-price').val(),
                category_id: $('#post-category').val()
            };
            makeApiCall('POST', `${baseUrl}/api/product`, data, 'post-product-response');
        }

        function testGetProduct() {
            const id = $('#get-product-id').val();
            if (!id) {
                alert('Vui lòng nhập ID sản phẩm');
                return;
            }
            makeApiCall('GET', `${baseUrl}/api/product/${id}`, null, 'get-product-response');
        }

        function testPutProduct() {
            const id = $('#put-id').val();
            if (!id) {
                alert('Vui lòng nhập ID sản phẩm');
                return;
            }
            const data = {
                name: $('#put-name').val(),
                description: $('#put-description').val(),
                price: $('#put-price').val(),
                category_id: $('#put-category').val()
            };
            makeApiCall('PUT', `${baseUrl}/api/product/${id}`, data, 'put-product-response');
        }

        function testDeleteProduct() {
            const id = $('#delete-product-id').val();
            if (!id) {
                alert('Vui lòng nhập ID sản phẩm');
                return;
            }
            if (!confirm('Bạn có chắc chắn muốn xóa sản phẩm này?')) {
                return;
            }
            makeApiCall('DELETE', `${baseUrl}/api/product/${id}`, null, 'delete-product-response');
        }

        function checkApiStatus() {
            const tests = [
                { name: 'Products API', url: `${baseUrl}/api/product` },
                { name: 'Categories API', url: `${baseUrl}/api/category` }
            ];

            let results = 'API Status Check:\n\n';
            let completed = 0;

            tests.forEach(function(test) {
                $.ajax({
                    url: test.url,
                    method: 'GET',
                    timeout: 5000,
                    success: function(data) {
                        results += `✅ ${test.name}: OK (${Array.isArray(data) ? data.length : 'N/A'} items)\n`;
                        completed++;
                        if (completed === tests.length) {
                            showResponse('api-status-response', results);
                        }
                    },
                    error: function(xhr, status, error) {
                        results += `❌ ${test.name}: ERROR (${error})\n`;
                        completed++;
                        if (completed === tests.length) {
                            showResponse('api-status-response', results);
                        }
                    }
                });
            });
        }

        function makeApiCall(method, url, data, responseElementId) {
            const startTime = new Date().getTime();
            
            $.ajax({
                url: url,
                method: method,
                contentType: 'application/json',
                data: data ? JSON.stringify(data) : null,
                success: function(response, status, xhr) {
                    const endTime = new Date().getTime();
                    const duration = endTime - startTime;
                    
                    const result = `Request: ${method} ${url}
Status: ${xhr.status} ${xhr.statusText}
Duration: ${duration}ms
Response:
${JSON.stringify(response, null, 2)}`;
                    
                    showResponse(responseElementId, result);
                },
                error: function(xhr, status, error) {
                    const endTime = new Date().getTime();
                    const duration = endTime - startTime;
                    
                    let response = 'No response body';
                    try {
                        response = JSON.stringify(JSON.parse(xhr.responseText), null, 2);
                    } catch (e) {
                        response = xhr.responseText || 'No response body';
                    }
                    
                    const result = `Request: ${method} ${url}
Status: ${xhr.status} ${xhr.statusText}
Duration: ${duration}ms
Error: ${error}
Response:
${response}`;
                    
                    showResponse(responseElementId, result);
                }
            });
        }

        function showResponse(elementId, content) {
            const element = $(`#${elementId}`);
            element.text(content);
            element.show();
        }
    </script>
</body>
</html>
