<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Frontend - Trang chủ</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .hero-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 80px 0;
        }
        .feature-card {
            transition: transform 0.3s;
            height: 100%;
        }
        .feature-card:hover {
            transform: translateY(-5px);
        }
    </style>
</head>
<body>
    <!-- Hero Section -->
    <div class="hero-section">
        <div class="container text-center">
            <h1 class="display-4 mb-4">
                <i class="fas fa-rocket"></i> API Frontend Demo
            </h1>
            <p class="lead mb-4">
                <PERSON><PERSON> thống quản lý sản phẩm với API RESTful và giao diện jQuery
            </p>
            <div class="row justify-content-center">
                <div class="col-md-8">
                    <div class="d-grid gap-2 d-md-flex justify-content-md-center">
                        <a href="app/views/product/api_frontend.php" class="btn btn-light btn-lg me-md-2">
                            <i class="fas fa-desktop"></i> Vào trang quản lý
                        </a>
                        <a href="api_demo.php" class="btn btn-outline-light btn-lg">
                            <i class="fas fa-code"></i> Test API
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Features Section -->
    <div class="container my-5">
        <div class="row text-center mb-5">
            <div class="col">
                <h2>Tính năng chính</h2>
                <p class="text-muted">Khám phá các tính năng mạnh mẽ của hệ thống</p>
            </div>
        </div>

        <div class="row">
            <!-- Feature 1 -->
            <div class="col-md-4 mb-4">
                <div class="card feature-card">
                    <div class="card-body text-center">
                        <div class="mb-3">
                            <i class="fas fa-boxes fa-3x text-primary"></i>
                        </div>
                        <h5 class="card-title">Quản lý sản phẩm</h5>
                        <p class="card-text">
                            Thêm, sửa, xóa và xem danh sách sản phẩm một cách dễ dàng với giao diện thân thiện.
                        </p>
                    </div>
                </div>
            </div>

            <!-- Feature 2 -->
            <div class="col-md-4 mb-4">
                <div class="card feature-card">
                    <div class="card-body text-center">
                        <div class="mb-3">
                            <i class="fas fa-search fa-3x text-success"></i>
                        </div>
                        <h5 class="card-title">Tìm kiếm & Lọc</h5>
                        <p class="card-text">
                            Tìm kiếm sản phẩm theo tên, mô tả và lọc theo danh mục một cách nhanh chóng.
                        </p>
                    </div>
                </div>
            </div>

            <!-- Feature 3 -->
            <div class="col-md-4 mb-4">
                <div class="card feature-card">
                    <div class="card-body text-center">
                        <div class="mb-3">
                            <i class="fas fa-code fa-3x text-warning"></i>
                        </div>
                        <h5 class="card-title">RESTful API</h5>
                        <p class="card-text">
                            API tuân thủ chuẩn REST với đầy đủ các phương thức GET, POST, PUT, DELETE.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Links -->
    <div class="bg-light py-5">
        <div class="container">
            <div class="row text-center">
                <div class="col">
                    <h3 class="mb-4">Truy cập nhanh</h3>
                </div>
            </div>
            <div class="row">
                <div class="col-md-6 mb-3">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">
                                <i class="fas fa-desktop text-primary"></i> Trang quản lý chính
                            </h5>
                            <p class="card-text">Giao diện đầy đủ để quản lý sản phẩm với jQuery</p>
                            <a href="app/views/product/api_frontend.php" class="btn btn-primary">
                                Vào trang quản lý
                            </a>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 mb-3">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">
                                <i class="fas fa-code text-success"></i> Trang test API
                            </h5>
                            <p class="card-text">Test từng endpoint API một cách chi tiết</p>
                            <a href="api_demo.php" class="btn btn-success">
                                Test API
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- API Endpoints -->
    <div class="container my-5">
        <div class="row">
            <div class="col">
                <h3 class="text-center mb-4">API Endpoints</h3>
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead class="table-dark">
                            <tr>
                                <th>Phương thức</th>
                                <th>Endpoint</th>
                                <th>Mô tả</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><span class="badge bg-primary">GET</span></td>
                                <td><code>/project1/api/product</code></td>
                                <td>Lấy danh sách tất cả sản phẩm</td>
                            </tr>
                            <tr>
                                <td><span class="badge bg-primary">GET</span></td>
                                <td><code>/project1/api/product/{id}</code></td>
                                <td>Lấy thông tin một sản phẩm</td>
                            </tr>
                            <tr>
                                <td><span class="badge bg-success">POST</span></td>
                                <td><code>/project1/api/product</code></td>
                                <td>Thêm sản phẩm mới</td>
                            </tr>
                            <tr>
                                <td><span class="badge bg-warning">PUT</span></td>
                                <td><code>/project1/api/product/{id}</code></td>
                                <td>Cập nhật sản phẩm</td>
                            </tr>
                            <tr>
                                <td><span class="badge bg-danger">DELETE</span></td>
                                <td><code>/project1/api/product/{id}</code></td>
                                <td>Xóa sản phẩm</td>
                            </tr>
                            <tr>
                                <td><span class="badge bg-primary">GET</span></td>
                                <td><code>/project1/api/category</code></td>
                                <td>Lấy danh sách danh mục</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white text-center py-3">
        <div class="container">
            <p class="mb-0">
                <i class="fas fa-heart text-danger"></i> 
                API Frontend Demo - Được xây dựng với jQuery & Bootstrap
            </p>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
