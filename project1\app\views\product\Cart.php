<?php include 'app/views/shares/header.php'; ?>

<!-- <PERSON><PERSON> with Animation -->
<div class="row mb-4 animate__animated animate__fadeIn">
    <div class="col-12">
        <div class="d-flex align-items-center justify-content-between">
            <h1 class="display-6 fw-bold text-primary">
                <i class="bi bi-cart3 me-2"></i>Giỏ hàng của bạn
            </h1>
            <?php if (!empty($cart)): ?>
                <span class="badge bg-primary fs-6 rounded-pill px-3 py-2"><?php echo count($cart); ?> sản phẩm</span>
            <?php endif; ?>
        </div>
        <hr class="border-primary border-2 opacity-50">
    </div>
</div>

<?php if (!empty($cart)): ?>
    <form method="POST" action="/project1/Product/updateCart">
        <div class="row">
            <!-- Cart Items -->
            <div class="col-lg-8 col-md-12 mb-4">
                <?php
                $total = 0;
                foreach ($cart as $id => $item):
                    $itemTotal = $item['price'] * $item['quantity'];
                    $total += $itemTotal;
                ?>
                    <div class="card mb-3 shadow-sm border-0 product-card animate__animated animate__fadeInUp">
                        <div class="card-body">
                            <div class="row g-3">
                                <!-- Product Image with Hover Effect -->
                                <div class="col-md-3 col-sm-4">
                                    <div class="text-center product-image-container">
                                        <?php if ($item['image']): ?>
                                            <img src="/project1/<?php echo $item['image']; ?>"
                                                 alt="<?php echo htmlspecialchars($item['name'], ENT_QUOTES, 'UTF-8'); ?>"
                                                 class="img-fluid rounded shadow-sm product-image"
                                                 style="max-height: 120px; object-fit: cover;">
                                        <?php else: ?>
                                            <div class="bg-light rounded d-flex align-items-center justify-content-center"
                                                 style="height: 120px;">
                                                <i class="bi bi-image text-muted" style="font-size: 2rem;"></i>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>

                                <!-- Product Info with Better Typography -->
                                <div class="col-md-6 col-sm-8">
                                    <h5 class="card-title text-dark fw-bold mb-2 product-name">
                                        <?php echo htmlspecialchars($item['name'], ENT_QUOTES, 'UTF-8'); ?>
                                    </h5>
                                    <p class="text-success fw-bold fs-5 mb-2 price-tag">
                                        <i class="bi bi-currency-dollar"></i>
                                        <?php echo number_format($item['price'], 0, ',', '.'); ?> VND
                                    </p>
                                    <p class="text-muted mb-2">
                                        <small>Thành tiền: <span class="fw-bold text-dark"><?php echo number_format($itemTotal, 0, ',', '.'); ?> VND</span></small>
                                    </p>
                                </div>

                                <!-- Quantity Controls with Enhanced Styling -->
                                <div class="col-md-3 col-12">
                                    <div class="d-flex flex-column align-items-center">
                                        <label class="form-label fw-bold text-muted mb-2">Số lượng</label>
                                        <div class="input-group quantity-control">
                                            <button type="button" class="btn btn-outline-secondary btn-sm quantity-btn" 
                                                    onclick="updateQuantity('<?php echo $id; ?>', -1)">
                                                <i class="bi bi-dash"></i>
                                            </button>
                                            <input type="number" name="quantity[<?php echo $id; ?>]" 
                                                   value="<?php echo $item['quantity']; ?>" 
                                                   min="0" class="form-control text-center quantity-input" 
                                                   id="quantity-<?php echo $id; ?>">
                                            <button type="button" class="btn btn-outline-secondary btn-sm quantity-btn" 
                                                    onclick="updateQuantity('<?php echo $id; ?>', 1)">
                                                <i class="bi bi-plus"></i>
                                            </button>
                                        </div>
                                        <a href="/project1/Product/removeFromCart/<?php echo $id; ?>" 
                                           class="btn btn-sm btn-outline-danger mt-2 remove-btn">
                                            <i class="bi bi-trash"></i> Xóa
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>

            <!-- Order Summary with Enhanced Design -->
            <div class="col-lg-4 col-md-12">
                <div class="card shadow-sm border-0 sticky-top order-summary animate__animated animate__fadeInRight" style="top: 20px;">
                    <div class="card-header bg-primary text-white">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-receipt me-2"></i>Tóm tắt đơn hàng
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="d-flex justify-content-between mb-2">
                            <span>Tạm tính:</span>
                            <span class="fw-bold"><?php echo number_format($total, 0, ',', '.'); ?> VND</span>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span>Phí vận chuyển:</span>
                            <span class="text-success fw-bold">Miễn phí</span>
                        </div>
                        <hr>
                        <div class="d-flex justify-content-between mb-3">
                            <span class="fw-bold fs-5">Tổng cộng:</span>
                            <span class="fw-bold fs-5 text-primary"><?php echo number_format($total, 0, ',', '.'); ?> VND</span>
                        </div>

                        <!-- Action Buttons with Hover Effects -->
                        <div class="d-grid gap-2">
                            <button type="submit" name="update_cart" class="btn btn-success update-btn">
                                <i class="bi bi-arrow-repeat me-2"></i>Cập nhật giỏ hàng
                            </button>
                            <a href="/project1/Product/checkout" class="btn btn-primary btn-lg checkout-btn">
                                <i class="bi bi-credit-card me-2"></i>Thanh toán
                            </a>
                            <a href="/project1/Product" class="btn btn-outline-secondary continue-btn">
                                <i class="bi bi-arrow-left me-2"></i>Tiếp tục mua sắm
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>

    <!-- JavaScript for quantity buttons -->
    <script>
        function updateQuantity(id, change) {
            const input = document.getElementById('quantity-' + id);
            let newValue = parseInt(input.value) + change;
            
            // Ensure quantity is at least 0
            if (newValue < 0) newValue = 0;
            
            input.value = newValue;
            
            // Add animation effect when changing quantity
            input.classList.add('quantity-changed');
            setTimeout(() => {
                input.classList.remove('quantity-changed');
            }, 300);
        }
    </script>

<?php else: ?>
    <!-- Empty Cart State with Animation -->
    <div class="row justify-content-center animate__animated animate__fadeIn">
        <div class="col-md-6 text-center">
            <div class="card border-0 shadow-sm empty-cart">
                <div class="card-body py-5">
                    <div class="mb-4 animate__animated animate__pulse animate__infinite">
                        <i class="bi bi-cart-x text-muted" style="font-size: 4rem;"></i>
                    </div>
                    <h3 class="text-muted mb-3">Giỏ hàng trống</h3>
                    <p class="text-muted mb-4">Bạn chưa có sản phẩm nào trong giỏ hàng. Hãy khám phá các sản phẩm tuyệt vời của chúng tôi!</p>
                    <a href="/project1/Product" class="btn btn-primary btn-lg explore-btn">
                        <i class="bi bi-shop me-2"></i>Khám phá sản phẩm
                    </a>
                </div>
            </div>
        </div>
    </div>
<?php endif; ?>

<!-- Custom CSS for Cart Page -->
<style>
    /* Product Card Styling */
    .product-card {
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        border-radius: 10px;
        overflow: hidden;
    }
    
    .product-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0,0,0,0.1) !important;
    }
    
    /* Product Image Styling */
    .product-image-container {
        overflow: hidden;
        border-radius: 8px;
    }
    
    .product-image {
        transition: transform 0.3s ease;
    }
    
    .product-image:hover {
        transform: scale(1.05);
    }
    
    /* Product Name Styling */
    .product-name {
        transition: color 0.3s ease;
    }
    
    .product-name:hover {
        color: #0d6efd !important;
    }
    
    /* Price Tag Styling */
    .price-tag {
        font-family: 'Roboto', sans-serif;
        letter-spacing: 0.5px;
    }
    
    /* Quantity Controls Styling */
    .quantity-control {
        width: 120px;
        border-radius: 30px;
        overflow: hidden;
    }
    
    .quantity-input {
        border: none;
        background-color: #f8f9fa;
        font-weight: bold;
    }
    
    .quantity-btn {
        background-color: #f1f1f1;
        border: none;
        transition: background-color 0.3s ease;
    }
    
    .quantity-btn:hover {
        background-color: #e2e6ea;
    }
    
    /* Animation for quantity change */
    .quantity-changed {
        animation: flash-highlight 0.3s ease;
    }
    
    @keyframes flash-highlight {
        0% { background-color: #f8f9fa; }
        50% { background-color: #e2f0ff; }
        100% { background-color: #f8f9fa; }
    }
    
    /* Remove Button Styling */
    .remove-btn {
        transition: all 0.3s ease;
        border-radius: 20px;
    }
    
    .remove-btn:hover {
        background-color: #dc3545;
        color: white;
    }
    
    /* Order Summary Styling */
    .order-summary {
        border-radius: 10px;
        overflow: hidden;
    }
    
    /* Action Buttons Styling */
    .update-btn, .checkout-btn, .continue-btn, .explore-btn {
        transition: all 0.3s ease;
        border-radius: 30px;
        font-weight: 500;
        letter-spacing: 0.5px;
    }
    
    .update-btn:hover, .checkout-btn:hover, .continue-btn:hover, .explore-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }
    
    /* Empty Cart Styling */
    .empty-cart {
        border-radius: 15px;
        transition: transform 0.3s ease;
    }
    
    .empty-cart:hover {
        transform: translateY(-5px);
    }
    
    /* Add animation classes */
    .animate__animated {
        animation-duration: 0.8s;
    }
</style>

<!-- Add Animate.css CDN for animations -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" />

<!-- Add Google Fonts for better typography -->
<link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">

<?php include 'app/views/shares/footer.php'; ?>
