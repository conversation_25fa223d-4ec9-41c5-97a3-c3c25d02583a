<?php include 'app/views/shares/header.php'; ?> 

<h1>Thê<PERSON> sản phẩm mới</h1> 

<form id="add-product-form"> 
    <div class="form-group"> 
        <label for="name">Tên sản phẩm:</label> 
        <input type="text" id="name" name="name" class="form-control" required> 
    </div>

    <div class="form-group"> 
        <label for="description">M<PERSON> tả:</label> 
        <textarea id="description" name="description" class="form-control" required></textarea> 
    </div> 

    <div class="form-group"> 
        <label for="price">Giá:</label> 
        <input type="number" id="price" name="price" class="form-control" step="0.01" required> 
    </div> 

    <div class="form-group">
        <label for="category_id">Danh mục:</label>
        <select id="category_id" name="category_id" class="form-control" required>
            <option value="">-- Chọ<PERSON> danh mục --</option>
            <!-- <PERSON><PERSON><PERSON> danh mục sẽ được tải từ API và hiển thị tại đây -->
        </select>
    </div>

    <button type="submit" class="btn btn-primary">Thêm sản phẩm</button> 
</form> 

<a href="/project1/Product/list" class="btn btn-secondary mt-2">Quay lại danh sách sản phẩm</a> 

<?php include 'app/views/shares/footer.php'; ?> 

<script> 
document.addEventListener("DOMContentLoaded", function() {
    const baseUrl = "/project1"; // ✅ Dễ dàng thay đổi toàn bộ URL

    // Tải danh mục vào dropdown
    fetch(`${baseUrl}/api/category`)
        .then(response => response.json())
        .then(data => {
            const categorySelect = document.getElementById('category_id');
            data.forEach(category => {
                const option = document.createElement('option');
                option.value = category.id;
                option.textContent = category.name;
                categorySelect.appendChild(option);
            });
        });

    // Xử lý sự kiện submit form
    document.getElementById('add-product-form').addEventListener('submit', function(event) {
        event.preventDefault();

        const formData = new FormData(this);
        const jsonData = {};
        formData.forEach((value, key) => {
            jsonData[key] = value;
        });

        // Validation phía client
        if (!jsonData.category_id || jsonData.category_id === '') {
            alert('Vui lòng chọn danh mục');
            return;
        }

        fetch(`${baseUrl}/api/product`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(jsonData)
        })
        .then(response => response.json())
        .then(data => {
            if (data.message === 'Product created successfully') {
                location.href = `${baseUrl}/Product`;
            } else {
                alert('Thêm sản phẩm thất bại');
            }
        })
        .catch(error => {
            console.error('Lỗi khi gửi yêu cầu:', error);
            alert('Đã xảy ra lỗi khi gửi yêu cầu.');
        });
    });
});
</script>
