<?php include 'app/views/shares/header.php'; ?> 

<h1><PERSON><PERSON><PERSON><PERSON> sản phẩm mới</h1> 

<form action="/project1/Product/save" method="POST" enctype="multipart/form-data">
    <div class="form-group">
        <label for="name">Tên sản phẩm:</label>
        <input type="text" id="name" name="name" class="form-control" required value="<?= isset($name) ? htmlspecialchars($name) : '' ?>">
        <?php if (isset($errors['name'])): ?>
            <div class="text-danger"><?= $errors['name'] ?></div>
        <?php endif; ?>
    </div>

    <div class="form-group">
        <label for="description">Mô tả:</label>
        <textarea id="description" name="description" class="form-control" required><?= isset($description) ? htmlspecialchars($description) : '' ?></textarea>
        <?php if (isset($errors['description'])): ?>
            <div class="text-danger"><?= $errors['description'] ?></div>
        <?php endif; ?>
    </div>

    <div class="form-group">
        <label for="price">Giá:</label>
        <input type="number" id="price" name="price" class="form-control" step="0.01" required value="<?= isset($price) ? htmlspecialchars($price) : '' ?>">
        <?php if (isset($errors['price'])): ?>
            <div class="text-danger"><?= $errors['price'] ?></div>
        <?php endif; ?>
    </div>

    <div class="form-group">
        <label for="category_id">Danh mục:</label>
        <select id="category_id" name="category_id" class="form-control" required>
            <option value="">-- Chọn danh mục --</option>
            <?php if (isset($categories)): ?>
                <?php foreach ($categories as $category): ?>
                    <option value="<?= $category->id ?>" <?= (isset($category_id) && $category_id == $category->id) ? 'selected' : '' ?>>
                        <?= htmlspecialchars($category->name) ?>
                    </option>
                <?php endforeach; ?>
            <?php endif; ?>
        </select>
        <?php if (isset($errors['category_id'])): ?>
            <div class="text-danger"><?= $errors['category_id'] ?></div>
        <?php endif; ?>
    </div>

    <div class="form-group">
        <label for="image">Hình ảnh:</label>
        <input type="file" id="image" name="image" class="form-control" accept="image/*">
    </div>

    <button type="submit" class="btn btn-primary">Thêm sản phẩm</button>
</form>

<a href="/project1/Product" class="btn btn-secondary mt-2">Quay lại danh sách sản phẩm</a>

<?php include 'app/views/shares/footer.php'; ?>
