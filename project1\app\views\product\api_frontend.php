<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quản lý sản phẩm - API Frontend</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }
        .product-card {
            transition: transform 0.2s;
        }
        .product-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .modal-backdrop {
            z-index: 1040;
        }
        .modal {
            z-index: 1050;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 bg-dark text-white p-3">
                <h4><i class="fas fa-store"></i> Quản lý sản phẩm</h4>
                <hr>
                <ul class="nav nav-pills flex-column">
                    <li class="nav-item">
                        <a class="nav-link active" href="#" id="products-tab">
                            <i class="fas fa-box"></i> Danh sách sản phẩm
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" id="add-product-tab">
                            <i class="fas fa-plus"></i> Thêm sản phẩm
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" id="categories-tab">
                            <i class="fas fa-tags"></i> Danh mục
                        </a>
                    </li>
                </ul>
            </div>

            <!-- Main content -->
            <div class="col-md-9 p-4">
                <!-- Products List Section -->
                <div id="products-section">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h2><i class="fas fa-box"></i> Danh sách sản phẩm</h2>
                        <button class="btn btn-primary" id="refresh-products">
                            <i class="fas fa-sync-alt"></i> Làm mới
                        </button>
                    </div>

                    <!-- Search and Filter -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <input type="text" class="form-control" id="search-products" placeholder="Tìm kiếm sản phẩm...">
                        </div>
                        <div class="col-md-3">
                            <select class="form-control" id="filter-category">
                                <option value="">Tất cả danh mục</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <select class="form-control" id="sort-products">
                                <option value="name">Sắp xếp theo tên</option>
                                <option value="price">Sắp xếp theo giá</option>
                                <option value="id">Sắp xếp theo ID</option>
                            </select>
                        </div>
                    </div>

                    <!-- Loading -->
                    <div class="loading" id="products-loading">
                        <i class="fas fa-spinner fa-spin fa-2x"></i>
                        <p>Đang tải dữ liệu...</p>
                    </div>

                    <!-- Products Grid -->
                    <div class="row" id="products-grid">
                        <!-- Products will be loaded here -->
                    </div>
                </div>

                <!-- Add Product Section -->
                <div id="add-product-section" style="display: none;">
                    <h2><i class="fas fa-plus"></i> Thêm sản phẩm mới</h2>
                    <div class="card">
                        <div class="card-body">
                            <form id="add-product-form">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="add-name" class="form-label">Tên sản phẩm *</label>
                                            <input type="text" class="form-control" id="add-name" name="name" required>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="add-price" class="form-label">Giá *</label>
                                            <input type="number" class="form-control" id="add-price" name="price" step="0.01" required>
                                        </div>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label for="add-description" class="form-label">Mô tả *</label>
                                    <textarea class="form-control" id="add-description" name="description" rows="3" required></textarea>
                                </div>
                                <div class="mb-3">
                                    <label for="add-category" class="form-label">Danh mục *</label>
                                    <select class="form-control" id="add-category" name="category_id" required>
                                        <option value="">-- Chọn danh mục --</option>
                                    </select>
                                </div>
                                <div class="d-flex gap-2">
                                    <button type="submit" class="btn btn-success">
                                        <i class="fas fa-save"></i> Lưu sản phẩm
                                    </button>
                                    <button type="reset" class="btn btn-secondary">
                                        <i class="fas fa-undo"></i> Đặt lại
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Categories Section -->
                <div id="categories-section" style="display: none;">
                    <h2><i class="fas fa-tags"></i> Danh mục sản phẩm</h2>
                    <div class="card">
                        <div class="card-body">
                            <div id="categories-list">
                                <!-- Categories will be loaded here -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Edit Product Modal -->
    <div class="modal fade" id="editProductModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Sửa sản phẩm</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="edit-product-form">
                        <input type="hidden" id="edit-id" name="id">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="edit-name" class="form-label">Tên sản phẩm *</label>
                                    <input type="text" class="form-control" id="edit-name" name="name" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="edit-price" class="form-label">Giá *</label>
                                    <input type="number" class="form-control" id="edit-price" name="price" step="0.01" required>
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="edit-description" class="form-label">Mô tả *</label>
                            <textarea class="form-control" id="edit-description" name="description" rows="3" required></textarea>
                        </div>
                        <div class="mb-3">
                            <label for="edit-category" class="form-label">Danh mục *</label>
                            <select class="form-control" id="edit-category" name="category_id" required>
                                <option value="">-- Chọn danh mục --</option>
                            </select>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Hủy</button>
                    <button type="button" class="btn btn-primary" id="save-edit-product">
                        <i class="fas fa-save"></i> Lưu thay đổi
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div class="modal fade" id="deleteProductModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Xác nhận xóa</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>Bạn có chắc chắn muốn xóa sản phẩm này không?</p>
                    <p><strong id="delete-product-name"></strong></p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Hủy</button>
                    <button type="button" class="btn btn-danger" id="confirm-delete-product">
                        <i class="fas fa-trash"></i> Xóa
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="/project1/public/js/api-frontend.js"></script>
</body>
</html>
