<?php include 'app/views/shares/header.php'; ?> 

<h1><PERSON><PERSON><PERSON> sản phẩm</h1> 

<form id="edit-product-form"> 
    <input type="hidden" id="id" name="id"> 

    <div class="form-group"> 
        <label for="name">Tên sản phẩm:</label> 
        <input type="text" id="name" name="name" class="form-control" required> 
    </div> 

    <div class="form-group"> 
        <label for="description"><PERSON><PERSON> tả:</label> 
        <textarea id="description" name="description" class="form-control" required></textarea> 
    </div> 

    <div class="form-group"> 
        <label for="price">Giá:</label>  
        <input type="number" id="price" name="price" class="form-control" step="0.01" required> 
    </div> 

    <div class="form-group">
        <label for="category_id">Danh mục:</label>
        <select id="category_id" name="category_id" class="form-control" required>
            <option value="">-- <PERSON><PERSON><PERSON> danh mục --</option>
            <!-- <PERSON><PERSON> mục sẽ được load từ API -->
        </select>
    </div>

    <button type="submit" class="btn btn-primary">Lưu thay đổi</button> 
</form> 

<a href="/project1/Product/list" class="btn btn-secondary mt-2">Quay lại danh sách sản phẩm</a> 

<?php include 'app/views/shares/footer.php'; ?> 

<script> 
document.addEventListener("DOMContentLoaded", function() {
    const baseUrl = "/project1";
    const productId = <?= $editId ?>;

    const form = document.getElementById('edit-product-form');

    // Lấy thông tin sản phẩm
    fetch(`${baseUrl}/api/product/${productId}`)
        .then(response => response.json())
        .then(product => {
            document.getElementById('id').value = product.id;
            document.getElementById('name').value = product.name;
            document.getElementById('description').value = product.description;
            document.getElementById('price').value = product.price;

            // Lấy danh mục để hiển thị
            fetch(`${baseUrl}/api/category`)
                .then(response => response.json())
                .then(categories => {
                    const select = document.getElementById('category_id');
                    categories.forEach(category => {
                        const option = document.createElement('option');
                        option.value = category.id;
                        option.textContent = category.name;
                        if (category.id == product.category_id) {
                            option.selected = true;
                        }
                        select.appendChild(option);
                    });
                });
        });

    // Gửi form cập nhật
    form.addEventListener('submit', function(event) {
        event.preventDefault();

        const formData = new FormData(form);
        const jsonData = {};
        formData.forEach((value, key) => {
            jsonData[key] = value;
        });

        // Validation phía client
        if (!jsonData.category_id || jsonData.category_id === '') {
            alert('Vui lòng chọn danh mục');
            return;
        }

        fetch(`${baseUrl}/api/product/${jsonData.id}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(jsonData)
        })
        .then(response => response.json())
        .then(data => {
            if (data.message === 'Product updated successfully') {
                window.location.href = `${baseUrl}/Product`;
            } else {
                alert('Cập nhật sản phẩm thất bại');
            }
        })
        .catch(error => {
            console.error('Lỗi:', error);
            alert('Đã xảy ra lỗi khi cập nhật sản phẩm');
        });
    });
});
</script>
