<?php
session_start();
require_once('app/config/database.php');
require_once('app/models/ProductModel.php');
require_once('app/models/CategoryModel.php');
require_once('app/helpers/SessionHelper.php');

echo "<h1>Debug Thêm Sản Phẩm</h1>";

// Kiểm tra session
echo "<h2>1. Ki<PERSON>m tra Session:</h2>";
echo "<p>Đã đăng nhập: " . (SessionHelper::isLoggedIn() ? "✅ Có" : "❌ Không") . "</p>";
echo "<p>Là Admin: " . (SessionHelper::isAdmin() ? "✅ Có" : "❌ Không") . "</p>";
echo "<p>Role: " . SessionHelper::getRole() . "</p>";

if (isset($_SESSION['username'])) {
    echo "<p>Username: " . $_SESSION['username'] . "</p>";
}

// Kiểm tra database connection
echo "<h2>2. Kiểm tra Database:</h2>";
try {
    $db = (new Database())->getConnection();
    echo "<p>Kết nối database: ✅ Thành công</p>";
    
    // Kiểm tra bảng product
    $stmt = $db->query("DESCRIBE product");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    echo "<p>Cấu trúc bảng product:</p>";
    echo "<pre>";
    print_r($columns);
    echo "</pre>";
    
} catch (Exception $e) {
    echo "<p>Lỗi database: ❌ " . $e->getMessage() . "</p>";
}

// Kiểm tra categories
echo "<h2>3. Kiểm tra Categories:</h2>";
try {
    $categoryModel = new CategoryModel($db);
    $categories = $categoryModel->getCategories();
    echo "<p>Số lượng categories: " . count($categories) . "</p>";
    echo "<pre>";
    print_r($categories);
    echo "</pre>";
} catch (Exception $e) {
    echo "<p>Lỗi lấy categories: ❌ " . $e->getMessage() . "</p>";
}

// Test thêm sản phẩm
echo "<h2>4. Test Thêm Sản Phẩm:</h2>";
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    echo "<h3>Dữ liệu POST nhận được:</h3>";
    echo "<pre>";
    print_r($_POST);
    echo "</pre>";
    
    echo "<h3>Files upload:</h3>";
    echo "<pre>";
    print_r($_FILES);
    echo "</pre>";
    
    if (SessionHelper::isAdmin()) {
        try {
            $productModel = new ProductModel($db);
            
            $name = $_POST['name'] ?? '';
            $description = $_POST['description'] ?? '';
            $price = $_POST['price'] ?? '';
            $category_id = $_POST['category_id'] ?? null;
            $image = '';
            
            echo "<h3>Dữ liệu sẽ insert:</h3>";
            echo "<p>Name: '$name'</p>";
            echo "<p>Description: '$description'</p>";
            echo "<p>Price: '$price'</p>";
            echo "<p>Category ID: '$category_id'</p>";
            echo "<p>Image: '$image'</p>";
            
            $result = $productModel->addProduct($name, $description, $price, $category_id, $image);
            
            echo "<h3>Kết quả:</h3>";
            if (is_array($result)) {
                echo "<p>❌ Có lỗi validation:</p>";
                echo "<pre>";
                print_r($result);
                echo "</pre>";
            } else if ($result === true) {
                echo "<p>✅ Thêm sản phẩm thành công!</p>";
                
                // Kiểm tra sản phẩm vừa thêm
                $products = $productModel->getProducts();
                echo "<p>Tổng số sản phẩm hiện tại: " . count($products) . "</p>";
                echo "<p>Sản phẩm cuối cùng:</p>";
                echo "<pre>";
                print_r(end($products));
                echo "</pre>";
            } else {
                echo "<p>❌ Thêm sản phẩm thất bại (không rõ lý do)</p>";
            }
            
        } catch (Exception $e) {
            echo "<p>❌ Lỗi exception: " . $e->getMessage() . "</p>";
        }
    } else {
        echo "<p>❌ Không có quyền admin</p>";
    }
}
?>

<h2>5. Form Test:</h2>
<form method="POST">
    <div style="margin-bottom: 10px;">
        <label>Tên sản phẩm:</label><br>
        <input type="text" name="name" value="Test Product <?= time() ?>" required style="width: 300px;">
    </div>
    
    <div style="margin-bottom: 10px;">
        <label>Mô tả:</label><br>
        <textarea name="description" required style="width: 300px; height: 60px;">Mô tả test sản phẩm</textarea>
    </div>
    
    <div style="margin-bottom: 10px;">
        <label>Giá:</label><br>
        <input type="number" name="price" value="100.00" step="0.01" required style="width: 300px;">
    </div>
    
    <div style="margin-bottom: 10px;">
        <label>Category ID:</label><br>
        <select name="category_id" required style="width: 300px;">
            <option value="">-- Chọn category --</option>
            <?php if (isset($categories)): ?>
                <?php foreach ($categories as $cat): ?>
                    <option value="<?= $cat->id ?>"><?= $cat->name ?></option>
                <?php endforeach; ?>
            <?php endif; ?>
        </select>
    </div>
    
    <button type="submit" style="padding: 10px 20px;">Test Thêm Sản Phẩm</button>
</form>

<h2>6. Danh sách sản phẩm hiện tại:</h2>
<?php
try {
    $productModel = new ProductModel($db);
    $products = $productModel->getProducts();
    echo "<p>Tổng số: " . count($products) . " sản phẩm</p>";
    
    if (count($products) > 0) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>Tên</th><th>Giá</th><th>Category</th></tr>";
        foreach ($products as $product) {
            echo "<tr>";
            echo "<td>" . $product->id . "</td>";
            echo "<td>" . htmlspecialchars($product->name) . "</td>";
            echo "<td>" . $product->price . "</td>";
            echo "<td>" . ($product->category_name ?? 'N/A') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
} catch (Exception $e) {
    echo "<p>Lỗi lấy danh sách: " . $e->getMessage() . "</p>";
}
?>
