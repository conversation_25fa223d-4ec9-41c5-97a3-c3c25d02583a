$(document).ready(function() {
    const baseUrl = '/project1';
    let products = [];
    let categories = [];
    let currentEditId = null;

    // Initialize
    init();

    function init() {
        loadCategories();
        loadProducts();
        bindEvents();
    }

    function bindEvents() {
        // Tab navigation
        $('#products-tab').click(function(e) {
            e.preventDefault();
            showSection('products');
            $(this).addClass('active').siblings().removeClass('active');
        });

        $('#add-product-tab').click(function(e) {
            e.preventDefault();
            showSection('add-product');
            $(this).addClass('active').siblings().removeClass('active');
        });

        $('#categories-tab').click(function(e) {
            e.preventDefault();
            showSection('categories');
            $(this).addClass('active').siblings().removeClass('active');
        });

        // Refresh products
        $('#refresh-products').click(function() {
            loadProducts();
        });

        // Search and filter
        $('#search-products').on('input', function() {
            filterProducts();
        });

        $('#filter-category').change(function() {
            filterProducts();
        });

        $('#sort-products').change(function() {
            sortProducts();
        });

        // Add product form
        $('#add-product-form').submit(function(e) {
            e.preventDefault();
            addProduct();
        });

        // Edit product
        $('#save-edit-product').click(function() {
            updateProduct();
        });

        // Delete product confirmation
        $('#confirm-delete-product').click(function() {
            deleteProduct();
        });
    }

    function showSection(section) {
        $('[id$="-section"]').hide();
        $(`#${section}-section`).show();
        
        if (section === 'categories') {
            loadCategories();
        }
    }

    function showLoading(show = true) {
        if (show) {
            $('#products-loading').show();
            $('#products-grid').hide();
        } else {
            $('#products-loading').hide();
            $('#products-grid').show();
        }
    }

    function showAlert(message, type = 'success') {
        const alertHtml = `
            <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
        $('.container-fluid').prepend(alertHtml);
        
        // Auto dismiss after 3 seconds
        setTimeout(function() {
            $('.alert').alert('close');
        }, 3000);
    }

    function loadProducts() {
        showLoading(true);
        
        $.ajax({
            url: `${baseUrl}/api/product`,
            method: 'GET',
            dataType: 'json',
            success: function(data) {
                products = data;
                renderProducts(products);
                showLoading(false);
            },
            error: function(xhr, status, error) {
                console.error('Error loading products:', error);
                showAlert('Lỗi khi tải danh sách sản phẩm', 'danger');
                showLoading(false);
            }
        });
    }

    function loadCategories() {
        $.ajax({
            url: `${baseUrl}/api/category`,
            method: 'GET',
            dataType: 'json',
            success: function(data) {
                categories = data;
                renderCategoryOptions();
                renderCategoriesList();
            },
            error: function(xhr, status, error) {
                console.error('Error loading categories:', error);
                showAlert('Lỗi khi tải danh mục', 'danger');
            }
        });
    }

    function renderProducts(productsToRender) {
        const grid = $('#products-grid');
        grid.empty();

        if (productsToRender.length === 0) {
            grid.html('<div class="col-12"><div class="alert alert-info">Không có sản phẩm nào.</div></div>');
            return;
        }

        productsToRender.forEach(function(product) {
            const productCard = `
                <div class="col-md-4 mb-4">
                    <div class="card product-card h-100">
                        <div class="card-body">
                            <h5 class="card-title">${product.name}</h5>
                            <p class="card-text">${product.description}</p>
                            <p class="card-text">
                                <strong>Giá: </strong>
                                <span class="text-success">${formatPrice(product.price)}</span>
                            </p>
                            <p class="card-text">
                                <small class="text-muted">
                                    Danh mục: ${product.category_name || 'Chưa phân loại'}
                                </small>
                            </p>
                        </div>
                        <div class="card-footer">
                            <div class="btn-group w-100" role="group">
                                <button class="btn btn-outline-primary btn-sm" onclick="editProduct(${product.id})">
                                    <i class="fas fa-edit"></i> Sửa
                                </button>
                                <button class="btn btn-outline-danger btn-sm" onclick="confirmDeleteProduct(${product.id}, '${product.name}')">
                                    <i class="fas fa-trash"></i> Xóa
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            grid.append(productCard);
        });
    }

    function renderCategoryOptions() {
        const selects = ['#add-category', '#edit-category', '#filter-category'];
        
        selects.forEach(function(selector) {
            const select = $(selector);
            const currentValue = select.val();
            
            // Keep first option for add/edit selects
            if (selector !== '#filter-category') {
                select.find('option:not(:first)').remove();
            } else {
                select.find('option:not(:first)').remove();
            }
            
            categories.forEach(function(category) {
                select.append(`<option value="${category.id}">${category.name}</option>`);
            });
            
            if (currentValue) {
                select.val(currentValue);
            }
        });
    }

    function renderCategoriesList() {
        const list = $('#categories-list');
        list.empty();

        if (categories.length === 0) {
            list.html('<div class="alert alert-info">Không có danh mục nào.</div>');
            return;
        }

        const table = `
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Tên danh mục</th>
                    </tr>
                </thead>
                <tbody>
                    ${categories.map(cat => `
                        <tr>
                            <td>${cat.id}</td>
                            <td>${cat.name}</td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
        `;
        list.html(table);
    }

    function filterProducts() {
        const searchTerm = $('#search-products').val().toLowerCase();
        const categoryFilter = $('#filter-category').val();
        
        let filtered = products.filter(function(product) {
            const matchesSearch = product.name.toLowerCase().includes(searchTerm) || 
                                product.description.toLowerCase().includes(searchTerm);
            const matchesCategory = !categoryFilter || product.category_id == categoryFilter;
            
            return matchesSearch && matchesCategory;
        });
        
        renderProducts(filtered);
    }

    function sortProducts() {
        const sortBy = $('#sort-products').val();
        
        products.sort(function(a, b) {
            if (sortBy === 'price') {
                return parseFloat(a.price) - parseFloat(b.price);
            } else if (sortBy === 'id') {
                return parseInt(a.id) - parseInt(b.id);
            } else {
                return a.name.localeCompare(b.name);
            }
        });
        
        filterProducts(); // Re-render with current filters
    }

    function addProduct() {
        const formData = {
            name: $('#add-name').val(),
            description: $('#add-description').val(),
            price: $('#add-price').val(),
            category_id: $('#add-category').val()
        };

        // Validation
        if (!formData.name || !formData.description || !formData.price || !formData.category_id) {
            showAlert('Vui lòng điền đầy đủ thông tin', 'warning');
            return;
        }

        $.ajax({
            url: `${baseUrl}/api/product`,
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify(formData),
            success: function(response) {
                showAlert('Thêm sản phẩm thành công!', 'success');
                $('#add-product-form')[0].reset();
                loadProducts();
                showSection('products');
                $('#products-tab').addClass('active').siblings().removeClass('active');
            },
            error: function(xhr, status, error) {
                console.error('Error adding product:', error);
                const response = xhr.responseJSON;
                if (response && response.errors) {
                    const errorMessages = Object.values(response.errors).join('<br>');
                    showAlert(errorMessages, 'danger');
                } else {
                    showAlert('Lỗi khi thêm sản phẩm', 'danger');
                }
            }
        });
    }

    // Global functions for onclick events
    window.editProduct = function(id) {
        currentEditId = id;
        
        $.ajax({
            url: `${baseUrl}/api/product/${id}`,
            method: 'GET',
            dataType: 'json',
            success: function(product) {
                $('#edit-id').val(product.id);
                $('#edit-name').val(product.name);
                $('#edit-description').val(product.description);
                $('#edit-price').val(product.price);
                $('#edit-category').val(product.category_id);
                
                $('#editProductModal').modal('show');
            },
            error: function(xhr, status, error) {
                console.error('Error loading product:', error);
                showAlert('Lỗi khi tải thông tin sản phẩm', 'danger');
            }
        });
    };

    window.confirmDeleteProduct = function(id, name) {
        currentEditId = id;
        $('#delete-product-name').text(name);
        $('#deleteProductModal').modal('show');
    };

    function updateProduct() {
        const formData = {
            id: $('#edit-id').val(),
            name: $('#edit-name').val(),
            description: $('#edit-description').val(),
            price: $('#edit-price').val(),
            category_id: $('#edit-category').val()
        };

        // Validation
        if (!formData.name || !formData.description || !formData.price || !formData.category_id) {
            showAlert('Vui lòng điền đầy đủ thông tin', 'warning');
            return;
        }

        $.ajax({
            url: `${baseUrl}/api/product/${currentEditId}`,
            method: 'PUT',
            contentType: 'application/json',
            data: JSON.stringify(formData),
            success: function(response) {
                showAlert('Cập nhật sản phẩm thành công!', 'success');
                $('#editProductModal').modal('hide');
                loadProducts();
            },
            error: function(xhr, status, error) {
                console.error('Error updating product:', error);
                showAlert('Lỗi khi cập nhật sản phẩm', 'danger');
            }
        });
    }

    function deleteProduct() {
        $.ajax({
            url: `${baseUrl}/api/product/${currentEditId}`,
            method: 'DELETE',
            success: function(response) {
                showAlert('Xóa sản phẩm thành công!', 'success');
                $('#deleteProductModal').modal('hide');
                loadProducts();
            },
            error: function(xhr, status, error) {
                console.error('Error deleting product:', error);
                showAlert('Lỗi khi xóa sản phẩm', 'danger');
            }
        });
    }

    function formatPrice(price) {
        return new Intl.NumberFormat('vi-VN', {
            style: 'currency',
            currency: 'VND'
        }).format(price);
    }
});
