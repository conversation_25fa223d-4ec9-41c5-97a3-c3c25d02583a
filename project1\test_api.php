<?php
// File test để kiểm tra API sau khi sửa lỗi
require_once('app/config/database.php');
require_once('app/models/ProductModel.php');

echo "<h1>Test API Product sau khi sửa lỗi</h1>";

// Kết nối database
$db = (new Database())->getConnection();
$productModel = new ProductModel($db);

echo "<h2>Test 1: Thêm sản phẩm với category_id hợp lệ</h2>";
$result1 = $productModel->addProduct('Test Product 1', 'Mô tả test', '100.00', '1');
if (is_array($result1)) {
    echo "Lỗi: ";
    print_r($result1);
} else {
    echo $result1 ? "✅ Thành công" : "❌ Thất bại";
}

echo "<h2>Test 2: Thêm sản phẩm với category_id = null</h2>";
$result2 = $productModel->addProduct('Test Product 2', 'Mô tả test 2', '200.00', null);
if (is_array($result2)) {
    echo "Lỗi: ";
    print_r($result2);
} else {
    echo $result2 ? "✅ Thành công" : "❌ Thất bại";
}

echo "<h2>Test 3: Thêm sản phẩm với category_id rỗng</h2>";
$result3 = $productModel->addProduct('Test Product 3', 'Mô tả test 3', '300.00', '');
if (is_array($result3)) {
    echo "Lỗi: ";
    print_r($result3);
} else {
    echo $result3 ? "✅ Thành công" : "❌ Thất bại";
}

echo "<h2>Test 4: Thêm sản phẩm với category_id không hợp lệ</h2>";
$result4 = $productModel->addProduct('Test Product 4', 'Mô tả test 4', '400.00', 'abc');
if (is_array($result4)) {
    echo "Lỗi (mong đợi): ";
    print_r($result4);
} else {
    echo $result4 ? "❌ Không nên thành công" : "✅ Thất bại như mong đợi";
}

echo "<h2>Danh sách sản phẩm hiện tại:</h2>";
$products = $productModel->getProducts();
echo "<pre>";
print_r($products);
echo "</pre>";
?>
