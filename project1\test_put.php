<?php
// Test PUT request
echo "<h1>Test PUT Request</h1>";

// Lấy một sản phẩm để test
require_once('app/config/database.php');
require_once('app/models/ProductModel.php');

$db = (new Database())->getConnection();
$productModel = new ProductModel($db);

// Lấy sản phẩm đầu tiên để test
$products = $productModel->getProducts();
if (empty($products)) {
    echo "Không có sản phẩm nào để test. Hãy tạo sản phẩm trước.";
    exit;
}

$testProduct = $products[0];
echo "<h2>Sản phẩm để test:</h2>";
echo "<pre>";
print_r($testProduct);
echo "</pre>";

echo "<h2>Test PUT với cURL:</h2>";

$url = "http://localhost/project1/api/product/" . $testProduct->id;
$data = [
    'name' => 'Updated Product Name',
    'description' => 'Updated description',
    'price' => '999.99',
    'category_id' => '1'
];

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $url);
curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "PUT");
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
    'Content-Length: ' . strlen(json_encode($data))
]);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

echo "<p><strong>URL:</strong> $url</p>";
echo "<p><strong>Data gửi:</strong></p>";
echo "<pre>" . json_encode($data, JSON_PRETTY_PRINT) . "</pre>";
echo "<p><strong>HTTP Code:</strong> $httpCode</p>";
echo "<p><strong>Response:</strong></p>";
echo "<pre>$response</pre>";

// Kiểm tra URL parsing
echo "<h2>Debug URL Parsing:</h2>";
$testUrl = "api/product/" . $testProduct->id;
$url_parts = explode('/', $testUrl);
echo "<p><strong>URL:</strong> $testUrl</p>";
echo "<p><strong>URL Parts:</strong></p>";
echo "<pre>";
print_r($url_parts);
echo "</pre>";
echo "<p><strong>Controller Name:</strong> " . (isset($url_parts[0]) && $url_parts[0] !== '' ? ucfirst($url_parts[0]) . 'Controller' : 'DefaultController') . "</p>";
echo "<p><strong>API Controller Name:</strong> " . (isset($url_parts[1]) ? ucfirst($url_parts[1]) . 'ApiController' : 'N/A') . "</p>";
echo "<p><strong>ID:</strong> " . ($url_parts[2] ?? 'N/A') . "</p>";
?>
